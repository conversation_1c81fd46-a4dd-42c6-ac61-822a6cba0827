import { Link as $Link, type LinkComponentProps } from '@tanstack/react-router'
import clsx from 'clsx'
import type { FC } from 'react'

export interface LinkStylesProps {
  variant?: 'colored' | 'navigation' | 'navigation-2' | 'bordered' | undefined
  className?: string | undefined
}

// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const useLinkStyles = ({ variant, className }: LinkStylesProps) =>
  clsx(
    'cursor-pointer', // styling
    {
      'text-primary underline': variant === 'colored',

      'py-4': variant === 'navigation-2',
      'py-5': variant === 'navigation',
      'flex gap-4 items-center': variant?.startsWith('navigation'), // flex
      'text-dim-1/80 text-xs font-semibold': variant === 'navigation', // styling
      'text-dim-1/80 text-2xs font-medium': variant === 'navigation-2',
      'px-8 rounded-b1': variant?.startsWith('navigation'), // sizing
      'transition-colors duration-300': variant?.startsWith('navigation'), // animation
      'hover:bg-whity hover:text-dim-1': variant?.startsWith('navigation'), // hover
      'data-[state=active]:bg-active data-[state=active]:text-primary': variant?.startsWith('navigation'), // other
      'border border-accessory-1 hover:bg-black/5 bg-white': variant === 'bordered',
      'px-8 py-5 rounded-b1 flex items-center justify-center gap-6 text-black text-xs': variant === 'bordered',
    },
    className,
  )

export const Link: FC<
  LinkComponentProps &
    LinkStylesProps & {
      to?: LinkComponentProps['to']
      children?: React.ReactNode
    }
> = ({ to, children, variant, className, ...props }) => {
  const styles = useLinkStyles({ variant, className })

  return (
    <$Link to={to} className={styles} viewTransition {...props}>
      {children}
    </$Link>
  )
}
