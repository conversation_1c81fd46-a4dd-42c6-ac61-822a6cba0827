import { ObservableHint, observable } from '@legendapp/state'
import { type BlobType, utcDate } from '@mass/utils'
import { type } from 'arktype'

export const subscriptionQueryValidation = type({
  period: "'yearly'",
  selectedDate: type('Date').array().atMostLength(2).atLeastLength(1),
  isRange: 'false',
  isLastX: 'false',
}).or(
  type({
    period: "'monthly' | 'daily'",
    selectedDate: type('Date').array().atMostLength(2).atLeastLength(1),
    isRange: 'false',
    isLastX: 'boolean',
  }).or(
    type({
      period: "'monthly' | 'daily'",
      selectedDate: type('Date').array().atMostLength(2).atLeastLength(2),
      isRange: 'boolean',
      isLastX: 'boolean',
    }),
  ),
)

export const ALLOWED_MONTH_COUNT = 5
export const ALLOWED_DAY_COUNT = 10

export const MAPPED_PERIODS = {
  yearly: 'year',
  monthly: 'month',
  daily: 'day',
} as const

export const MAPPED_COMPARISONS = {
  average: 'average',
  cityEntry: 'city-average',
  districtEntry: 'district-average',
} as const

export const subscription$ = observable<Dashboard.Subscription>({
  regions: null,
  subscriptions: null,

  selectedSubscription: null,

  usageLimits: null,

  query: {
    values: {
      period: 'yearly',
      isRange: false,
      isLastX: false,
      selectedDate: [utcDate().startOf('year').toDate()],
    },

    errors: {
      period: false,
      isRange: false,
      isLastX: false,
      selectedDate: false,
    },

    dirty: {
      period: false,
      isRange: false,
      isLastX: false,
      selectedDate: false,
    },

    setPeriod: ObservableHint.function(period => {
      if (period === 'yearly') {
        subscription$.query.values.isRange.set(false)
        subscription$.query.values.isLastX.set(false)
      }

      let newStartDate: Date

      if (period === 'yearly') {
        newStartDate = utcDate().startOf('year').toDate()
      } else if (period === 'monthly') {
        newStartDate = utcDate().startOf('month').toDate()
      } else {
        newStartDate = utcDate().startOf('day').toDate()
      }

      subscription$.query.values.selectedDate.set([newStartDate])

      subscription$.query.values.period.set(period)
      subscription$.query.setDirty('period')
    }),

    setIsRange: ObservableHint.function(isRange => {
      if (isRange) {
        subscription$.query.values.isLastX.set(false)
      } else {
        subscription$.query.values.selectedDate.set(prev => {
          if (prev.length === 2) {
            prev.pop()
          }

          return prev
        })
      }

      subscription$.query.values.isRange.set(isRange)
      subscription$.query.setDirty('isRange')
    }),

    setIsLastX: ObservableHint.function(isLastX => {
      if (isLastX) {
        subscription$.query.values.isRange.set(false)
      }

      subscription$.query.values.isLastX.set(isLastX)
      subscription$.query.setDirty('isLastX')
    }),

    setSelectedDate: ObservableHint.function(selectedDate => {
      subscription$.query.values.selectedDate.set(selectedDate)
      subscription$.query.setDirty('selectedDate')
    }),

    isError: ObservableHint.function(key => {
      const value = subscription$.query.values[key].get()
      const dirty = subscription$.query.dirty[key].get()

      return dirty && subscriptionQueryValidation(value) instanceof type.errors
    }),
    setDirty: ObservableHint.function(key => {
      subscription$.query.errors[key].set(
        subscriptionQueryValidation.get(key)(subscription$.query.values[key].get()) instanceof type.errors,
      )

      if (subscriptionQueryValidation.get(key)(subscription$.query.values[key].get()) instanceof type.errors) {
        console.log(subscriptionQueryValidation.get(key)(subscription$.query.values[key].get()))
      }

      subscription$.query.dirty[key].set(true)
      subscription$.usage.clear()
    }),

    checkAll: ObservableHint.function(() => {
      subscription$.query.setDirty('period')
      subscription$.query.setDirty('isRange')
      subscription$.query.setDirty('isLastX')
      subscription$.query.setDirty('selectedDate')

      const out = subscriptionQueryValidation(subscription$.query.values.get())

      if (out instanceof type.errors) {
        for (const error of out) {
          const errors = (error as BlobType).errors

          if (Array.isArray(errors) && errors !== null && errors[0].propString) {
            subscription$.query.errors[errors[0].propString as 'period' | 'isRange' | 'isLastX' | 'selectedDate'].set(
              true,
            )
          }
        }
      }
    }),

    startYearOptions: () => {
      // biome-ignore lint/style/noNonNullAssertion: Redundant
      const max = subscription$.usageLimits.max.year.get()!

      return Array.from(new Array(max), (_el, index) => utcDate().subtract(index, 'year').startOf('year').toDate())
    },

    startMonthOptions: () => {
      // biome-ignore lint/style/noNonNullAssertion: Redundant
      const max = subscription$.usageLimits.max.month.get()!

      return Array.from(new Array(max), (_el, index) => utcDate().subtract(index, 'month').startOf('month').toDate())
    },
    endMonthOptions: () => {
      const startMonthOptions = subscription$.query.startMonthOptions.get()

      const startDate = subscription$.query.values.selectedDate.get().at(0)

      if (!startDate) {
        return []
      }

      const indexOfSelected = startMonthOptions.findIndex(
        date => date.getFullYear() === startDate.getFullYear() && date.getMonth() === startDate.getMonth(),
      )

      const remainingMonths = startMonthOptions.slice(0, indexOfSelected)

      if (remainingMonths.length > ALLOWED_MONTH_COUNT) {
        return remainingMonths.slice(remainingMonths.length - ALLOWED_MONTH_COUNT)
      }

      return remainingMonths
    },

    populated: () => {
      const { period, isRange, isLastX, selectedDate } = subscription$.query.values.get()

      let granularity: Api.ExtractParams<Api.Services['subscriptions']['usage']['data']>['granularity'] = 'month'

      if (period === 'yearly') {
        granularity = 'month'
      } else if (period === 'monthly') {
        granularity = isRange || isLastX ? 'month' : 'hour'
      } else if (period === 'daily') {
        granularity = isRange || isLastX ? 'day' : 'hour'
      }

      let rawStartDate = selectedDate[0]
      let rawEndDate = selectedDate[1]

      if (isLastX && period !== 'yearly') {
        const prevName = period === 'monthly' ? 'month' : 'day'
        // biome-ignore lint/style/noNonNullAssertion: Redundant
        const prev = subscription$.usageLimits.prev[prevName].get()!

        rawEndDate = rawStartDate
        rawStartDate = utcDate(rawEndDate).subtract(prev, prevName).toDate()
        rawEndDate = utcDate(rawEndDate).add(1, prevName).toDate()
      }

      if (isRange && period !== 'yearly') {
        const prevName = period === 'monthly' ? 'month' : 'day'

        rawEndDate = utcDate(rawEndDate).add(1, prevName).toDate()
      }

      if (!selectedDate[0]) {
        return null
      }

      const startDate = utcDate(rawStartDate).startOf(granularity).toISOString()
      const endDate = (rawEndDate ? utcDate(rawEndDate) : utcDate(rawStartDate).add(1, MAPPED_PERIODS[period]))
        .startOf(granularity)
        .toISOString()

      return {
        startDate,
        endDate,
        granularity,
        compareTo: ['average', 'similar-consumer-city', 'similar-consumer-district'],
      }
    },

    clear: ObservableHint.function(() => {
      subscription$.query.values.set({
        period: 'yearly',
        isRange: false,
        isLastX: false,
        selectedDate: [utcDate().startOf('year').toDate()],
      })

      subscription$.query.errors.set({
        period: false,
        isRange: false,
        isLastX: false,
        selectedDate: false,
      })

      subscription$.query.dirty.set({
        period: false,
        isRange: false,
        isLastX: false,
        selectedDate: false,
      })
    }),
  },

  usage: {
    data: null,
    isLoading: false,

    mode: 'chart',
    timeMode: 'monochronic',

    populated: () => {
      const result: Dashboard.PopulatedUsages[] = []
      let comparisonResult: Dashboard.PopulatedUsages[] = []

      const isProduction = subscription$.selectedSubscription.get()?.type === 'electricity-production'
      const populatedGranularity = subscription$.query.populated.granularity.peek() ?? 'day'
      const mode = subscription$.usage.mode.get()
      const period = subscription$.query.values.period.get()

      const consumptionComparisonData = subscription$.usage.data.get()?.consumption?.compare
      const productionComparisonData = subscription$.usage.data.get()?.production?.compare

      if (consumptionComparisonData) {
        const keys = Object.keys(consumptionComparisonData) as (keyof typeof consumptionComparisonData)[]

        for (const key of keys) {
          const comparison: Dashboard.PopulatedUsages = {
            name: MAPPED_COMPARISONS[key],

            t0: consumptionComparisonData[key].T0,
            t1: consumptionComparisonData[key].T1,
            t2: consumptionComparisonData[key].T2,
            t3: consumptionComparisonData[key].T3,
          }

          if (key === 'average' && isProduction && productionComparisonData) {
            comparison.p0 = subscription$.usage.data.get()?.production?.compare?.average.T1 ?? 0
          }

          comparisonResult.push(comparison)
        }
      }

      // SHOW THE AVERAGES OF THE USAGES ONLY IF THE GRANULARITY IS HOURLY AND THE MODE IS TABLE
      if (populatedGranularity === 'hour' && mode === 'table') {
        return [...comparisonResult, ...subscription$.usage.populatedAverages.get()]
      }

      if (populatedGranularity === 'hour' && mode === 'chart' && period === 'monthly') {
        return [...subscription$.usage.populatedAverages.get(), ...comparisonResult]
      }

      // DONT SHOW AVERAGES IF TABLE MODE IS HOURLY AND DATA IS HOURLY
      if (populatedGranularity === 'hour' && mode === 'hourly') {
        comparisonResult = []
      }

      const consumptionDatas = subscription$.usage.data.get()?.consumption.usageData ?? []

      for (const consumptionData of consumptionDatas) {
        result.push({
          name: utcDate(consumptionData.timeframe).startOf(populatedGranularity).toDate(),

          t0: consumptionData.data.T0,
          t1: consumptionData.data.T1,
          t2: consumptionData.data.T2,
          t3: consumptionData.data.T3,
        })
      }

      if (!isProduction) {
        return mode === 'chart' ? [...result, ...comparisonResult] : [...comparisonResult, ...result]
      }

      const productionDatas = subscription$.usage.data.get()?.production?.usageData ?? []

      for (const productionData of productionDatas) {
        const targetIndex = result.findIndex(
          data => +data.name === +utcDate(productionData.timeframe).startOf(populatedGranularity).toDate(),
        )

        if (targetIndex === -1) {
          continue
        }

        // biome-ignore lint/style/noNonNullAssertion: Redundant
        result[targetIndex]!.p0 = productionData.data.T1
      }

      return mode === 'chart' ? [...result, ...comparisonResult] : [...comparisonResult, ...result]
    },

    populatedAverages: () => {
      const result: Dashboard.PopulatedUsages[] = []

      const consumptionDatas = subscription$.usage.data.get()?.consumption.usageData ?? []

      if (consumptionDatas.length === 0) {
        return result
      }

      const period = subscription$.query.values.period.get()

      result.push({
        // biome-ignore lint/style/noNonNullAssertion: Redundant
        name: utcDate(consumptionDatas.at(0)!.timeframe).startOf(MAPPED_PERIODS[period]).toDate(),

        t0:
          consumptionDatas.reduce((acc, consumptionData) => acc + consumptionData.data.T0, 0) / consumptionDatas.length,
        t1:
          consumptionDatas.reduce((acc, consumptionData) => acc + consumptionData.data.T1, 0) / consumptionDatas.length,
        t2:
          consumptionDatas.reduce((acc, consumptionData) => acc + consumptionData.data.T2, 0) / consumptionDatas.length,
        t3:
          consumptionDatas.reduce((acc, consumptionData) => acc + consumptionData.data.T3, 0) / consumptionDatas.length,
      })

      const isProduction = subscription$.selectedSubscription.get()?.type === 'electricity-production'
      const productionDatas = subscription$.usage.data.get()?.production?.usageData ?? []

      if (!isProduction || productionDatas.length === 0) {
        return result
      }

      // biome-ignore lint/style/noNonNullAssertion: Redundant
      result.at(-1)!.p0 =
        productionDatas.reduce((acc, productionData) => acc + productionData.data.T1, 0) / productionDatas.length

      return result
    },

    clear: ObservableHint.function(() => {
      subscription$.usage.data.set(null)
      subscription$.usage.isLoading.set(false)

      subscription$.usage.mode.set('chart')
      subscription$.usage.timeMode.set('monochronic')
    }),
  },
})
