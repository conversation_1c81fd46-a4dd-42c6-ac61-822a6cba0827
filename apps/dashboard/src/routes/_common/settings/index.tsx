/** biome-ignore-all lint/nursery/noExcessiveLinesPerFunction: Redundant */
import { ObservableHint } from '@legendapp/state'
import { use$ } from '@legendapp/state/react'
import { type END_USER_TYPE, global$, settingApi } from '@mass/api'
import { Button, Input, Link, Select, Text, Title, ui$ } from '@mass/components/shared'
import { ExternalIcon } from '@mass/icons'
import type { BlobType } from '@mass/utils'
import { createFileRoute } from '@tanstack/react-router'
import clsx from 'clsx'
import { useCallback, useState } from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'

function Settings() {
  const { t: common } = useTranslation('common')
  const { t: dashboard } = useTranslation('dashboard')

  const [isLoading, setIsLoading] = useState(false)
  const user = use$(() => global$.user.get() as unknown as (typeof END_USER_TYPE)['infer'])

  const handleDeleteAccount = useCallback(async () => {
    setIsLoading(true)

    try {
      const { promise, resolve } = Promise.withResolvers<boolean>()

      ui$.confirmation.set({
        title: dashboard('subscriptions.delete'),
        description: dashboard('subscriptions.delete-description'),
        onConfirm: ObservableHint.function(() => resolve(true)) as never,
        onCancel: ObservableHint.function(() => resolve(false)) as never,
        toast: false,
      })

      ui$.onChangeModal('confirmation', true)

      const result = await promise

      if (!result) {
        return toast.success(common('operation-canceled'))
      }

      await toast.promise(
        async () => {
          await settingApi.deleteUser()
        },
        {
          loading: common('wait'),
          success: common('operation-completed'),
          error: common('something-went-wrong'),
        },
      )
    } catch (err) {
      console.log(err)
      toast.error(common('something-went-wrong'))
    } finally {
      setIsLoading(false)
    }
  }, [common, dashboard])

  return (
    <div
      className={clsx(
        'flex flex-col', // flex
        'gap-8 p-8 md:p-16', // spacing
      )}>
      <div
        className={clsx(
          'flex flex-col justify-between lg:flex-row', // flex
          'gap-8 pb-8 md:pb-16', // spacing
          'border-accessory-1 border-b', // border
          'relative w-full overflow-hidden',
        )}>
        <div className='flex flex-col gap-2'>
          <Title el='h1' variant='h4'>
            {common('personal-informations')}
          </Title>

          <Text variant='dim-2'> {dashboard('settings.personal-informations-description')} </Text>
        </div>
      </div>

      <div
        className={clsx(
          'flex flex-col justify-between sm:flex-row sm:items-center', // flex
          'gap-4 pb-8 md:gap-6', // spacing
          'border-accessory-2 border-b', // border
          // 'max-w-[1200px]',
        )}>
        <Title el='h2' variant='h5' className='text-nowrap'>
          {common('name')}
        </Title>
        <div className={clsx('flex justify-end', 'w-full gap-4')}>
          <Input value={user?.firstName ?? ''} placeholder={common('name')} className='max-w-[250px]' disabled />
          <Input value={user?.lastName ?? ''} placeholder={common('surname')} className='max-w-[250px]' disabled />
        </div>
      </div>

      <div
        className={clsx(
          'flex flex-col justify-between sm:flex-row sm:items-center', // flex
          'gap-4 pb-8 md:gap-6', // spacing
          'border-accessory-2 border-b', // border
          // 'max-w-[1200px]',
        )}>
        <Title el='h2' variant='h5' className='text-nowrap'>
          {common('person-identifier-real')}
        </Title>
        <div className={clsx('flex justify-end', 'w-full gap-4')}>
          <Input
            value={user?.tckn ?? ''}
            placeholder={common('person-identifier-real')}
            className='max-w-[508px]'
            disabled
          />
        </div>
      </div>

      <div
        className={clsx(
          'flex flex-col justify-between sm:flex-row sm:items-center', // flex
          'gap-4 pb-8 md:gap-6', // spacing
          'border-accessory-2 border-b', // border
          // 'max-w-[1200px]',
        )}>
        <Title el='h2' variant='h5' className='text-nowrap'>
          {common('language')}
        </Title>
        <div className={clsx('flex justify-end', 'w-full gap-4')}>
          <Select
            value={global$.language.get() ?? 'tr'}
            options={[
              { value: 'tr', label: 'Türkçe' },
              { value: 'en', label: 'English' },
            ]}
            onValueChange={value => {
              settingApi.changeLangauge(value, global$.language.get())
            }}
            className='sm:max-w-[250px]'
          />
        </div>
      </div>

      <div
        className={clsx(
          'flex flex-col justify-between sm:flex-row sm:items-center', // flex
          'gap-4 pb-8 md:gap-6', // spacing
          'border-accessory-2 border-b', // border
          // 'max-w-[1200px]',
        )}>
        <Title el='h2' variant='h5' className='text-nowrap'>
          {common('documents')}
        </Title>
        <div className={clsx('flex justify-end', 'w-full gap-4 sm:max-w-[250px]')}>
          <Link
            to={(global$.documents.pdfs.kvkk.get() ?? '#') as BlobType}
            target='_blank'
            variant='bordered'
            className='w-full items-center justify-between'>
            {common('pdpl')} <ExternalIcon width={16} height={16} />
          </Link>

          <Link
            to={(global$.documents.pdfs.agreement.get() ?? '#') as BlobType}
            target='_blank'
            variant='bordered'
            className='w-full items-center justify-between'>
            {common('agreement')} <ExternalIcon width={16} height={16} />
          </Link>
        </div>
      </div>

      <div
        className={clsx(
          'flex flex-col items-center justify-between gap-4 md:flex-row', // flex
          'rounded-b1 bg-error-lighter p-8', // styling
          'border border-error-light',
        )}>
        <div className={clsx('flex flex-col gap-4 sm:max-w-[600px]')}>
          <Title el='h2' variant='h2' className='text-nowrap'>
            {dashboard('settings.delete-account.title')}
          </Title>

          <Text variant='dim-2'> {dashboard('settings.delete-account.description')} </Text>
        </div>

        <Button
          variant='error'
          className='w-full text-nowrap sm:w-max'
          onClick={handleDeleteAccount}
          disabled={isLoading}>
          {dashboard('settings.delete-account.title')}
        </Button>
      </div>
    </div>
  )
}

export const Route = createFileRoute('/_common/settings/')({
  component: Settings,
})
